/* eslint-disable sonarjs/no-hardcoded-ip */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  buildAuditTrailTarget,
  finalizeAuditTrailEntry,
  formatEntityInfo,
  initializeAuditMeta,
  prepareAuditTrailEntry,
} from '#src/utils/audit-trail.util.js';

import { VIEW_ACTION } from '#src/modules/core/constants/core.constant.js';

import geoip from 'geoip-lite';

const createMockRequest = (overrides = {}) => ({
  id: 'req-id',
  url: '/api/some-endpoint',
  method: 'POST',
  headers: {
    'x-forwarded-for': '*******',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
    host: 'localhost:3000',
    origin: 'http://localhost',
  },
  query: { id: 123 },
  body: { data: 'value' },
  authInfo: {
    authAccess: 'user',
    id: 'user-123',
    username: 'tester',
    role: 'admin',
    department: 'IT',
    fingerprintId: 'abc123',
  },
  entity: {
    id: 'e-1',
    code: 'E1',
    prefix: 'ENT',
    name: 'Entity One',
    hierarchy: 'organization',
  },
  entityAccessId: 'access-001',
  hierarchyLevel: 'level-1',
  parentEntity: null,
  responsePayload: {
    message: 'Some error occurred',
    errorCode: 'ERR001',
    meta: { detail: 'Detailed error' },
  },
  ...overrides,
});

const createMockFastify = () => ({
  config: { KAFKA: true },
  kafka: {
    producer: {
      send: vi.fn().mockResolvedValue(true),
    },
  },
  redis: {
    setex: vi.fn().mockResolvedValue(true),
  },
  log: {
    error: vi.fn(),
    debug: vi.fn(),
  },
});

describe('Audit Trail Utility', () => {
  describe('prepareAuditTrailEntry', () => {
    it('should create auditEntries with correct defaults', () => {
      const fastify = {};
      const request = createMockRequest();

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries).toBeDefined();
      expect(request.auditEntries.actor.id).toBe('user-123');
      expect(request.auditEntries.context.ip).toBe('*******');
    });

    it('should fallback to UNKNOWN_LOCATION when geoip lookup fails', () => {
      const request = createMockRequest({ ip: '0.0.0.0' });
      vi.spyOn(geoip, 'lookup').mockReturnValueOnce(null);
      prepareAuditTrailEntry({}, request);
      expect(request.auditEntries.context.location).toBe('UNKNOWN_LOCATION');
    });

    it('should fallback to UNKNOWN_CITY and UNKNOWN_COUNTRY when geoip returns incomplete data', () => {
      const request = createMockRequest({ ip: '*******' });
      vi.spyOn(geoip, 'lookup').mockReturnValueOnce({});
      prepareAuditTrailEntry({}, request);
      expect(request.auditEntries.context.location).toBe('UNKNOWN_CITY, UNKNOWN_COUNTRY');
    });

    it('should fallback to unknown device info if user-agent is missing', () => {
      const request = createMockRequest({
        headers: { ...createMockRequest().headers, 'user-agent': '' },
      });
      prepareAuditTrailEntry({}, request);
      expect(request.auditEntries.context.device).toBe(
        'UNKNOWN_DEVICE - UNKNOWN_OS - UNKNOWN_BROWSER',
      );
    });

    it('should fallback to Desktop, UNKNOWN_OS, and UNKNOWN_BROWSER if UA parts are missing', () => {
      const request = createMockRequest({
        headers: { ...createMockRequest().headers, 'user-agent': 'unknown-agent' },
      });

      const parsedUA = {
        device: {},
        os: {},
        browser: {},
      };
      vi.doMock('ua-parser-js', () => {
        return vi.fn().mockReturnValue(parsedUA);
      });

      prepareAuditTrailEntry({}, request);

      expect(request.auditEntries.context.device).toBe('Desktop - UNKNOWN_OS - UNKNOWN_BROWSER');
    });

    it('should set organization and merchant when hierarchy is merchant', () => {
      const fastify = {};
      const request = createMockRequest({
        entity: {
          hierarchy: 'merchant',
          id: 'merchant1',
          code: 'M1',
          name: 'Merchant One',
          prefix: 'MER',
        },
        parentEntity: {
          hierarchy: 'organization',
          id: 'org1',
          name: 'Org One',
          code: 'O1',
          name: 'Organization One',
          prefix: 'ORG',
        },
      });

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries).toBeDefined();
      expect(request.auditEntries.actor.organization).toEqual({
        id: 'org1',
        name: 'Organization One',
        code: 'O1',
        prefix: 'ORG',
      });

      expect(request.auditEntries.actor.merchant).toEqual({
        id: 'merchant1',
        name: 'Merchant One',
        code: 'M1',
        prefix: 'MER',
      });
    });

    it('should set organization when hierarchy is organization', () => {
      const fastify = {};
      const request = createMockRequest({
        entity: {
          hierarchy: 'organization',
          id: 'org1',
          name: 'Org One',
          code: 'O1',
          name: 'Organization One',
          prefix: 'ORG',
        },
      });

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries.actor.organization).toEqual({
        id: 'org1',
        name: 'Organization One',
        code: 'O1',
        prefix: 'ORG',
      });

      expect(request.auditEntries.actor.merchant).toBeNull();
      expect(request.auditEntries.actor.root).toBeNull();
    });

    it('should set root when hierarchy is root', () => {
      const fastify = {};
      const request = createMockRequest({
        entity: {
          hierarchy: 'root',
          id: 'root1',
          name: 'Root',
          code: 'RT1',
          name: 'Root One',
          prefix: 'RT',
        },
      });

      prepareAuditTrailEntry(fastify, request);

      expect(request.auditEntries.actor.root).toEqual({
        id: 'root1',
        name: 'Root One',
        code: 'RT1',
        prefix: 'RT',
      });

      expect(request.auditEntries.actor.organization).toBeNull();
      expect(request.auditEntries.actor.merchant).toBeNull();
    });
  });

  describe('formatEntityInfo', () => {
    it('should return null when entity is undefined', () => {
      expect(formatEntityInfo(undefined)).toBeNull();
    });

    it('should return null when entity is null', () => {
      expect(formatEntityInfo(null)).toBeNull();
    });

    it('should fallback missing fields with default "UNKNOWN"', () => {
      const entity = {
        id: null,
      };

      expect(formatEntityInfo(entity)).toEqual({
        id: 'UNKNOWN_ID',
        code: 'UNKNOWN_CODE',
        prefix: 'UNKNOWN_PREFIX',
        name: 'UNKNOWN_NAME',
      });
    });
  });

  describe('initializeAuditMeta', () => {
    const baseMeta = {
      module: 'user',
    };

    it('should fallback to UNKNOWN_USER if username is missing', async () => {
      const mockRequest = {
        authInfo: {},
        entity: { hierarchy: 'merchant', name: 'Test Entity' },
      };

      await initializeAuditMeta(mockRequest, baseMeta, '001');

      expect(mockRequest.auditEntries.descriptionParams.username).toBe('UNKNOWN_USER');
    });

    it('should fallback to UNKNOWN_HIERARCHY if entity.hierarchy is missing', async () => {
      const mockRequest = {
        authInfo: { username: 'jane.doe' },
        entity: { name: 'Entity Name' },
      };

      await initializeAuditMeta(mockRequest, baseMeta, '002');

      expect(mockRequest.auditEntries.descriptionParams.hierarchy).toBe('UNKNOWN_HIERARCHY');
    });

    it('should fallback to UNKNOWN_ENTITY if entity.name is missing', async () => {
      const mockRequest = {
        authInfo: { username: 'jane.doe' },
        entity: { hierarchy: 'organization' },
      };

      await initializeAuditMeta(mockRequest, baseMeta, '002');

      expect(mockRequest.auditEntries.descriptionParams.entityName).toBe('UNKNOWN_ENTITY');
    });

    it('should initialize auditEntries if not present', async () => {
      const mockRequest = {};

      await initializeAuditMeta(mockRequest, baseMeta);

      expect(mockRequest.auditEntries).toBeDefined();
      expect(mockRequest.auditEntries.descriptionParams.username).toBe('UNKNOWN_USER');
    });
  });

  describe('buildAuditTrailTarget', () => {
    let mockRequest;
    beforeEach(() => {
      mockRequest = createMockRequest();
      prepareAuditTrailEntry({}, mockRequest);
    });

    it('should construct audit trail targets for VIEW_ACTION and call buildViewTargets logic', () => {
      const model = 'User';
      const referenceId = 'user-abc';
      const referenceDetails = { name: 'Test User' };

      const modelMapping = {
        [model]: {
          beforeState: { id: referenceId, name: 'Test User' },
          afterState: { id: referenceId, name: 'Test User' },
          // For VIEW_ACTION, fieldsChanged can be empty or not provided, as changes are not tracked
          fieldsChanged: [],
          referenceDetails: referenceDetails,
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: VIEW_ACTION.VIEWED_DETAILS, // Use a specific VIEW_ACTION
        isMultiple: false,
        metrics: { duration: 100, dbQueries: 5 },
        status: 'Success',
      };

      // Set a status code on the request if it's expected to be populated
      mockRequest.statusCode = 200;

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toBeDefined();
      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0]).toEqual({
        model: model,
        referenceId: referenceId,
        referenceDetails: referenceDetails,
        changes: undefined, // View actions don't track specific field changes
      });
      expect(mockRequest.auditEntries.statusCode).toBe(200);
      expect(mockRequest.auditEntries.details.metrics).toEqual({ duration: 100, dbQueries: 5 });
      expect(mockRequest.auditEntries.descriptionParams.referenceIds).toBe(referenceId);
      expect(mockRequest.auditEntries.descriptionParams.status).toBe('Success');
    });

    it('should construct audit trail targets for single UPDATE action', () => {
      const model = 'Product';
      const referenceId = 'prod-xyz';
      const referenceDetails = { code: 'PROD001' };

      const modelMapping = {
        [model]: {
          beforeState: { id: referenceId, name: 'Old Product Name', price: 100, status: 'ACTIVE' },
          afterState: { id: referenceId, name: 'New Product Name', price: 100, status: 'ACTIVE' },
          fieldsChanged: ['name'],
          referenceDetails: referenceDetails,
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        isMultiple: false,
        metrics: {},
        status: 'Success',
      };

      mockRequest.statusCode = 200;

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toBeDefined();
      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0]).toEqual({
        model: model,
        referenceId: referenceId,
        referenceDetails: referenceDetails,
        changes: {
          beforeState: { name: 'Old Product Name' },
          afterState: { name: 'New Product Name' },
        },
      });
      expect(mockRequest.auditEntries.statusCode).toBe(200);
      expect(mockRequest.auditEntries.descriptionParams.referenceIds).toBe(referenceId);
      expect(mockRequest.auditEntries.descriptionParams.status).toBe('Success');
    });

    it('should construct audit trail targets for multiple UPDATE action and only include changed fields', () => {
      const model = 'Attribute';

      const modelMapping = {
        [model]: {
          beforeState: {
            field1: { id: 'attr1', value: 'oldVal1' },
            field2: { id: 'attr2', value: 'oldVal2' },
          },
          afterState: {
            field1: 'newVal1',
            field2: 'oldVal2',
          },
          fieldsChanged: ['field1'],
          referenceDetails: {
            field1: { type: 'color' },
            field2: { type: 'size' },
          },
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        isMultiple: true,
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toBeDefined();
      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0]).toEqual({
        model: model,
        referenceId: 'attr1',
        referenceDetails: { type: 'color' },
        changes: {
          beforeState: { field1: 'oldVal1' },
          afterState: { field1: 'newVal1' },
        },
      });
      expect(mockRequest.auditEntries.statusCode).toBe(200);
      expect(mockRequest.auditEntries.descriptionParams.referenceIds).toBe('attr1');
      expect(mockRequest.auditEntries.descriptionParams.status).toBe('Success');
    });

    it('should handle target with missing referenceId for multi-update', () => {
      const model = 'Attribute';

      const modelMapping = {
        [model]: {
          beforeState: {
            field1: { value: 'oldVal1' }, // Missing ID
          },
          afterState: {
            field1: 'newVal1',
          },
          fieldsChanged: ['field1'],
          referenceDetails: {
            field1: { type: 'color' },
          },
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        isMultiple: true,
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target[0].referenceId).toBe('UNKNOWN_ID');
    });

    it('should handle object with dataValues for extractData', () => {
      const model = 'User';
      const referenceId = 'user-dataValues';
      const referenceDetails = { name: 'DataValues Test' };

      const modelMapping = {
        [model]: {
          beforeState: { dataValues: { id: referenceId, name: 'Old DataValues' } },
          afterState: { dataValues: { id: referenceId, name: 'New DataValues' } },
          fieldsChanged: ['name'],
          referenceDetails: referenceDetails,
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        isMultiple: false,
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target[0].changes.beforeState.name).toBe('Old DataValues');
      expect(mockRequest.auditEntries.target[0].changes.afterState.name).toBe('New DataValues');
      expect(mockRequest.auditEntries.target[0].referenceId).toBe(referenceId);
    });

    it('should derive changed fields from afterState for CREATE action', () => {
      const model = 'NewRecord';

      const modelMapping = {
        [model]: {
          afterState: { fieldA: 'valueA', fieldB: 'valueB' },
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'CREATED',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0].changes.beforeState).toEqual({});
      expect(mockRequest.auditEntries.target[0].changes.afterState).toEqual({
        fieldA: 'valueA',
        fieldB: 'valueB',
      });
    });

    it('should derive changed fields from beforeState for DELETE action', () => {
      const model = 'OldRecord';

      const modelMapping = {
        [model]: {
          beforeState: { id: 'old-rec-id', fieldX: 'valueX', fieldY: 'valueY' },
        },
      };

      const options = {
        modelMapping: modelMapping,
        action: 'DELETED',
      };

      buildAuditTrailTarget(mockRequest, options);

      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0].changes.beforeState).toEqual({
        id: 'old-rec-id',
        fieldX: 'valueX',
        fieldY: 'valueY',
      });
      expect(mockRequest.auditEntries.target[0].changes.afterState).toEqual({});
    });

    it('should handle isMultiple=true with empty fieldsChanged array resulting in no targets', () => {
      const model = 'TestMulti';
      const modelMapping = {
        [model]: {
          beforeState: { item1: { id: 'id1', value: 'val1' } },
          afterState: { item1: { id: 'id1', value: 'val1' } },
          fieldsChanged: [], // No actual changes
          referenceDetails: { item1: { type: 'A' } },
        },
      };
      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        isMultiple: true,
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);
      expect(mockRequest.auditEntries.target).toEqual([]);
      expect(mockRequest.auditEntries.descriptionParams.referenceIds).toBe('');
    });

    it('should handle isMultiple=true when afterField value is null/undefined', () => {
      const model = 'TestMultiNullAfter';
      const modelMapping = {
        [model]: {
          beforeState: {
            fieldB: { id: 'uuid2', value: 'oldValue' },
          },
          afterState: {
            fieldB: null, // After is null
          },
          fieldsChanged: ['fieldB'],
          referenceDetails: {
            fieldB: { name: 'Field B' },
          },
        },
      };
      const options = {
        modelMapping: modelMapping,
        action: 'UPDATED',
        isMultiple: true,
        metrics: {},
        status: 'Success',
      };

      buildAuditTrailTarget(mockRequest, options);
      expect(mockRequest.auditEntries.target).toHaveLength(1);
      expect(mockRequest.auditEntries.target[0].changes).toEqual({
        beforeState: { fieldB: 'oldValue' },
        afterState: { fieldB: null },
      });
      expect(mockRequest.auditEntries.target[0].referenceId).toBe('uuid2');
    });
  });

  describe('finalizeAuditTrailEntry', () => {
    it('should update status and send message to Kafka on success', async () => {
      const fastify = createMockFastify();
      const request = createMockRequest();
      const reply = { statusCode: 200 };

      prepareAuditTrailEntry(fastify, request);
      await finalizeAuditTrailEntry(fastify, request, reply);

      expect(request.auditEntries.status).toBe('Success');
      expect(fastify.kafka.producer.send).toHaveBeenCalled();
    });

    it('should mark audit as failed and include error details if response failed', async () => {
      const fastify = createMockFastify();
      const request = createMockRequest();
      const reply = { statusCode: 500 };

      prepareAuditTrailEntry(fastify, request);
      await finalizeAuditTrailEntry(fastify, request, reply);

      expect(request.auditEntries.status).toBe('Failed');
      expect(request.auditEntries.details.error.message).toBe('Some error occurred');
      expect(fastify.kafka.producer.send).toHaveBeenCalled();
    });

    it('should store message in Redis if Kafka send fails', async () => {
      const fastify = createMockFastify();
      fastify.kafka.producer.send.mockRejectedValue(new Error('Kafka error'));
      const request = createMockRequest();
      const reply = { statusCode: 200 };

      prepareAuditTrailEntry(fastify, request);
      await finalizeAuditTrailEntry(fastify, request, reply);

      expect(fastify.log.error).toHaveBeenCalledWith(expect.any(Error), 'Kafka send failed');
      expect(fastify.log.debug).toHaveBeenCalledWith(
        expect.any(Array),
        'Kafka message stored in Redis',
      );
    });
  });
});

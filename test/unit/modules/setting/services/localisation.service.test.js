import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as localisationService from '#src/modules/setting/services/localisation.service.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { LocalisationConstant } from '#src/modules/setting/constants/index.js';
import { LocalisationError } from '#src/modules/setting/errors/index.js';
import { LocalisationRepository } from '#src/modules/setting/repository/index.js';
import { fetchFromCache } from '#src/utils/cache.util.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/utils/cache.util.js');

const { LOCALISATION_CATEGORIES } = LocalisationConstant;
const { CURRENCY, LANGUAGE, REGION } = LOCALISATION_CATEGORIES;
const { ROOT, ORGANIZATION, MERCHANT } = CoreConstant.ACCESS_LEVELS;
const { INACTIVE } = CoreConstant.COMMON_STATUSES;

describe('Localisation Service', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      entity: { id: 'entity1', hierarchy: ROOT },
      server: {
        systemSetting: {
          baseCurrency: {
            code: 'USD',
          },
        },
        redis: {},
      },
      params: { id: 'loc1' },
      body: {},
      authInfo: { id: 'user1' },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should return localisation entries with base currency', async () => {
      const mockRows = [{ toJSON: () => ({ id: 'loc1', name: 'Localisation 1' }) }];
      LocalisationRepository.findAll.mockResolvedValue({ rows: mockRows });

      const result = await localisationService.index(mockRequest);

      expect(result.rows).toEqual([{ id: 'loc1', name: 'Localisation 1', baseCurrency: 'USD' }]);
    });
  });

  describe('generateDropdown', () => {
    it('should return cached dropdown data if available', async () => {
      const cachedData = [{ id: 'loc1', name: 'Localisation 1' }];
      fetchFromCache.mockResolvedValue(cachedData);

      const result = await localisationService.generateDropdown({
        ...mockRequest,
        query: { filter_category: CURRENCY },
      });

      expect(result).toEqual(cachedData);
    });

    it('should generate dropdown data if not cached', async () => {
      fetchFromCache.mockImplementation((_, __, fn) => fn());
      const mockRows = [
        {
          id: 'loc1',
          name: 'Localisation 1',
          toJSON: () => ({ id: 'loc1', name: 'Localisation 1' }),
        },
      ];
      LocalisationRepository.findAll.mockResolvedValue({ rows: mockRows });

      const result = await localisationService.generateDropdown({
        ...mockRequest,
        query: { filter_category: CURRENCY },
      });

      expect(result).toEqual([{ id: 'loc1', name: 'Localisation 1' }]);
    });
  });

  describe('view', () => {
    it('should return localisation entry with base currency', async () => {
      const mockLocalisationData = {
        id: 'loc1',
        name: 'Localisation 1',
        category: 'currency',
      };
      const mockLocalisation = {
        toJSON: () => mockLocalisationData,
      };
      LocalisationRepository.findById.mockResolvedValue(mockLocalisation);

      const result = await localisationService.view(mockRequest, mockRequest.params.id);

      expect(result).toEqual({
        ...mockLocalisationData,
        baseCurrency: 'USD',
      });
    });

    it('should throw error if localisation not found', async () => {
      LocalisationRepository.findById.mockResolvedValue(null);

      await expect(localisationService.view(mockRequest, mockRequest.params.id)).rejects.toThrow(
        LocalisationError.notFound('loc1'),
      );
    });
  });

  describe('update', () => {
    it('should update status', async () => {
      const mockLocalisation = {
        id: 'loc1',
        parentId: 'parent1',
        status: CoreConstant.COMMON_STATUSES.ACTIVE,
        toJSON: function () {
          return { id: this.id, parentId: this.parentId, status: this.status };
        },
      };
      LocalisationRepository.findById.mockResolvedValue(mockLocalisation);

      LocalisationRepository.update.mockImplementation(async (loc, body) => {
        loc.status = body.status;
        return loc;
      });

      mockRequest.body = { status: INACTIVE };
      const result = await localisationService.updateStatus(mockRequest, mockRequest.params.id);

      // The result should contain the audit object
      expect(result.result).toEqual({ id: 'loc1', parentId: 'parent1', status: INACTIVE });
      expect(result.audit.beforeState).toEqual({
        id: 'loc1',
        parentId: 'parent1',
        status: CoreConstant.COMMON_STATUSES.ACTIVE,
      });
      expect(result.audit.afterState).toEqual({
        id: 'loc1',
        parentId: 'parent1',
        status: INACTIVE,
      });
      expect(result.audit.fieldsChanged).toEqual(['status']);
    });

    it('should throw notFound error when id not found', async () => {
      LocalisationRepository.findById.mockResolvedValue(null);

      await expect(localisationService.update(mockRequest, mockRequest.params.id)).rejects.toThrow(
        LocalisationError.notFound(mockRequest.params.id),
      );
    });

    it('should throw error for missing exchange rate', async () => {
      const mockLocalisation = { category: CURRENCY };
      LocalisationRepository.findById.mockResolvedValue(mockLocalisation);

      mockRequest.body = {};
      await expect(localisationService.update(mockRequest, mockRequest.params.id)).rejects.toThrow(
        LocalisationError.invalidData('exchangeRate is required for currency'),
      );
    });

    it('should throw error for unsupported update', async () => {
      const mockLocalisation = { category: LANGUAGE };
      LocalisationRepository.findById.mockResolvedValue(mockLocalisation);

      await expect(localisationService.update(mockRequest, mockRequest.params.id)).rejects.toThrow(
        LocalisationError.unsupportedUpdate(LANGUAGE),
      );
    });

    it('should update a CURRENCY localisation entry and return audit info', async () => {
      const mockBeforeState = {
        id: 'loc1',
        category: CURRENCY,
        code: 'USD',
        exchangeRate: 1.0,
        name: 'US Dollar',
      };
      const mockAfterState = {
        id: 'loc1',
        category: CURRENCY,
        code: 'USD',
        exchangeRate: 1.05, // Changed value
        name: 'US Dollar',
      };

      const mockLocalisation = {
        category: CURRENCY,
        code: 'USD',
        toJSON: vi.fn(() => mockBeforeState), // Mock toJSON for beforeState
      };
      const mockUpdatedLocalisation = {
        category: CURRENCY,
        code: 'USD',
        toJSON: vi.fn(() => mockAfterState), // Mock toJSON for afterState
      };

      LocalisationRepository.findById.mockResolvedValueOnce(mockLocalisation);
      LocalisationRepository.update.mockResolvedValueOnce(mockUpdatedLocalisation);

      mockRequest.body = { exchangeRate: 1.05 };

      const result = await localisationService.update(mockRequest, mockRequest.params.id);

      expect(LocalisationRepository.findById).toHaveBeenCalledWith(mockRequest.server, {
        filter_id_eq: mockRequest.params.id,
        filter_entityId_eq: mockRequest.entity.id,
      });
      expect(mockLocalisation.toJSON).toHaveBeenCalledTimes(1); // For beforeState
      expect(LocalisationRepository.update).toHaveBeenCalledWith(
        mockLocalisation,
        mockRequest.body,
        mockRequest.authInfo,
      );
      expect(mockUpdatedLocalisation.toJSON).toHaveBeenCalledTimes(1); // For afterState

      expect(result).toEqual({
        result: mockAfterState,
        audit: {
          beforeState: mockBeforeState,
          afterState: mockAfterState,
          fieldsChanged: ['exchangeRate'],
          referenceDetails: {
            category: mockLocalisation.category,
            code: mockLocalisation.code,
          },
        },
      });
    });
  });

  describe('updateStatus', () => {
    it('should update status', async () => {
      const mockLocalisation = {
        id: 'loc1',
        parentId: 'parent1',
        status: CoreConstant.COMMON_STATUSES.ACTIVE,
        toJSON: function () {
          return { id: this.id, parentId: this.parentId, status: this.status };
        },
      };
      LocalisationRepository.findById.mockResolvedValue(mockLocalisation);

      LocalisationRepository.update.mockResolvedValue({
        id: 'loc1',
        parentId: 'parent1',
        status: INACTIVE,
        toJSON: function () {
          return { id: this.id, parentId: this.parentId, status: this.status };
        },
      });
      mockRequest.body = { status: INACTIVE };
      const result = await localisationService.updateStatus(mockRequest, mockRequest.params.id);

      expect(result.result).toEqual({ id: 'loc1', parentId: 'parent1', status: INACTIVE });
      expect(result.audit.beforeState).toEqual({
        id: 'loc1',
        parentId: 'parent1',
        status: CoreConstant.COMMON_STATUSES.ACTIVE,
      });
      expect(result.audit.afterState).toEqual({
        id: 'loc1',
        parentId: 'parent1',
        status: INACTIVE,
      });
      expect(result.audit.fieldsChanged).toEqual(['status']);
    });

    it('should throw notFound error when not id not found', async () => {
      LocalisationRepository.findById.mockResolvedValue(null);

      await expect(
        localisationService.updateStatus(mockRequest, mockRequest.params.id),
      ).rejects.toThrow(LocalisationError.notFound(mockRequest.params.id));
    });

    it('should throw error if localisation is in use by child entities', async () => {
      const mockLocalisation = { id: 'loc1', parentId: 'parent1' };
      LocalisationRepository.findById.mockResolvedValue(mockLocalisation);
      LocalisationRepository.findActiveByParentId.mockResolvedValue({ id: 'childLoc1' });

      mockRequest.body = { status: INACTIVE };
      await expect(
        localisationService.updateStatus(mockRequest, mockRequest.params.id),
      ).rejects.toThrow(LocalisationError.inUse('loc1'));
    });
  });
});

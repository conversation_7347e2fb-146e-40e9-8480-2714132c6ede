import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MongoosePlugin } from '#src/plugins/mongoose.plugin.js';
import mongoose from 'mongoose';

// Mock the mongoose module
vi.mock('mongoose', () => ({
  default: {
    connect: vi.fn(),
    connection: {},
  },
}));

describe('MongoosePlugin', () => {
  let mongoosePlugin;
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      config: {
        MONGO_ROOT_USERNAME: 'user',
        // eslint-disable-line sonarjs/no-hardcoded-passwords
        MONGO_ROOT_PASSWORD: 'pass',
        MONGO_HOST: 'localhost',
        MONGO_PORT: '27017',
        MONGO_DB: 'testdb',
      },
      log: {
        info: vi.fn(),
      },
      decorate: vi.fn(),
    };

    mongoosePlugin = new MongoosePlugin(mockFastify);
  });

  describe('connect method', () => {
    it('should connect to MongoDB with correct URI', async () => {
      await mongoosePlugin.connect();

      const expectedUri = '******************************************';
      expect(mongoose.connect).toHaveBeenCalledWith(expectedUri);
      expect(mockFastify.log.info).toHaveBeenCalledWith('MongoDB connection established');
    });

    it('should throw an error if connection fails', async () => {
      mongoose.connect.mockRejectedValue(new Error('Connection failed'));

      await expect(mongoosePlugin.connect()).rejects.toThrow('Connection failed');
    });
  });

  describe('decorate method', () => {
    it('should decorate fastify instance with mongo connection', () => {
      mongoosePlugin.decorate();

      expect(mockFastify.decorate).toHaveBeenCalledWith('mongo', {
        connection: mongoose.connection,
      });
    });

    it('should not throw if decorate is called multiple times', () => {
      expect(() => {
        mongoosePlugin.decorate();
        mongoosePlugin.decorate();
      }).not.toThrow();
    });
  });
});

export const ACCESS_LEVELS = {
  MERCHANT: 'merchant',
  ORGANIZATION: 'organization',
  ROOT: 'root',
  USER: 'user',
};

export const ACCESS_LEVEL_KEYS = {
  user: 'user',
  member: 'member',
  webhook: 'webhook',
};

export const CACHE_SECOND = {
  SHORT: 10, // For rapidly changing data or debounce-type caching
  MEDIUM: 30, // 30 second – suitable for moderately volatile data
  STANDARD: 60, // 1 minute – good default for most general cache
  LONG: 3600, // 1 hour – stable data that changes infrequently
  DAILY: 86400, // 24 hours – rarely changing reference data
  WEEKLY: 604800, // 7 days – archive-type or external lookup cache
  NEVER: 0, // Used when caching is disabled
};

export const COMMON_STATUSES = {
  ACTIVE: 'active',
  DELETED: 'deleted',
  INACTIVE: 'inactive',
};

export const EVENTS = {
  APP_CENTER: 'appCenter',
  AUDIT_TRAIL: 'auditTrail',
  AUTOMATION: 'automation',
  DEVELOPER_HUB: 'developerHub',
  GAME_PROVIDER: 'gameProvider',
  LOGIN: 'login',
  LOGOUT: 'logout',
  MAINTENANCE: 'maintenance',
  MANAGE_MONITOR: 'manageMonitor',
  MEDIA: 'media',
  MEMBER: 'member',
  MEMBER_POINT: 'memberPoint',
  MEMBER_VIP: 'memberVIP',
  MERCHANT: 'merchant',
  MERCHANT_CREDIT: 'merchantCredit',
  ORGANIZATION: 'organization',
  OTP: 'OTP',
  PERSONAL_SETTINGS: 'personalSettings',
  REGISTRATION_FORM: 'registrationForm',
  RESET_PASSWORD: 'resetPassword',
  RISK_GROUP: 'riskGroup',
  ROLE: 'role',
  SETTINGS: {
    PERSONAL: 'systemPersonalSettings',
    SAFETY: 'systemSecuritySafety',
    THEME: 'systemThemeSettings',
  },
  SYSTEM_CREDIT_LIMIT_SETTINGS: 'systemCreditLimitSettings',
  SYSTEM_CURRENCY_SETTINGS: 'systemCurrencySettings',
  SYSTEM_DATA_MASKING_SETTINGS: 'systemDataMaskingSettings',
  SYSTEM_LANGUAGE_SETTINGS: 'systemLanguageSettings',
  SYSTEM_LOCALISATION_SETTINGS: 'systemLocalisationSettings',
  SETTINGS_OPTIONS: {
    PERSONAL: 'personalSettingsOptions',
    SAFETY: 'securitySafetySettingsOptions',
    THEME: 'themeSettingsOptions',
  },
  SYSTEM_REGION_SETTINGS: 'systemRegionSettings',
  SYSTEM_SECURITY_ACCESS_CONTROL: 'systemSecurityAccessControl',
  TAGS: 'tags',
  TRANSACTIONS: 'transactions',
  TRIGGERED_EVENT_LOG: 'triggeredEventLog',
  TWO_FACTOR_AUTHENTICATION: 'twoFactorAuthentication',
  USER: 'user',
  USER_AUDIT_TRAIL: 'userAuditTrail',
  USER_EXTERNAL_INVITATION: 'userExternalInvitation',
  USER_HIERARCHY: 'userHierarchy',
  USER_LOGIN_LOG: 'userLoginLog',
  USER_SSO: 'userSSO',
  USER_SUB_ACCOUNT: 'userSubAccount',
};

export const EVENT_ACTIONS = {
  ARCHIVED: 'archived',
  ASSIGNED: 'assigned',
  CREATED: 'created',
  DELETED: 'deleted',
  DUPLICATED: 'duplicated',
  EDITED: 'edited',
  EXPORTED: 'exported',
  GENERATED_API_KEY: 'generatedAPIKey',
  IMPORTED: 'imported',
  INSTALLED: 'installed',
  IP_BLACKLISTED: 'IPBlacklisted',
  KILL_SWITCH: 'killSwitch',
  LOGIN: 'login',
  LOGOUT: 'logout',
  RAN_TEST: 'ranTest',
  RESET: 'reset',
  SEARCHED: 'searched',
  SETUP: 'setup',
  UNINSTALLED: 'uninstalled',
  UPDATED: 'updated',
  UPDATED_RISK_SCORE: 'updatedRiskScore',
  UPDATED_STATUS: 'updatedStatus',
  VIEWED: 'viewed',
  VIEWED_DETAILS: 'viewedDetails',
  VIEWED_HISTORY: 'viewedHistory',
};

export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const MODULE_METHODS = {
  CREATE: 'create',
  DELETE: 'delete',
  EXPORT: 'export',
  INDEX: 'index',
  OPTION: 'option',
  UPDATE: 'update',
  UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
  UPDATE_PERSONAL: 'updatePersonal',
  UPDATE_SAFETY: 'updateSafety',
  UPDATE_STATUS: 'updateStatus',
  UPDATE_THEMES: 'updateThemes',
  VIEW: 'view',
};

export const MODULE_NAMES = {
  ACCESS_CONTROL: 'accessControls',
  AUDIT_TRAIL: 'auditTrails',
  BULK_JOB: 'bulkJobs',
  CORE: 'core',
  DEVELOPER_HUB: 'developerHubs',
  LOCALISATION: 'localisations',
  SETTING: 'settings',
};

export const REDACT_FIELDS = {
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirmPassword',
};

export const REMARK_STATUSES = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
};

export const REMARK_TYPE = {
  AUDIT: 'audit',
  NOTE: 'note',
  SECURITY: 'security',
  SYSTEM: 'system',
  WARNING: 'warning',
};

export const REMARKABLE_TYPE = {
  IP_ACCESS_CONTROL: 'ip_access_control',
};

export const VIEW_ACTION = {
  SEARCHED: 'searched',
  VIEWED: 'viewed',
  VIEWED_DETAILS: 'viewedDetails',
};

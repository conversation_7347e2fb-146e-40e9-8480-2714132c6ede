import { LocalisationConstant, SettingConstant } from '#src/modules/setting/constants/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { SettingError } from '#src/modules/setting/errors/index.js';
import { SettingRepository } from '#src/modules/setting/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const {
  SETTING_CATEGORIES: { PERSONAL, SAFETY, THEMES },
  APPEARANCE_MODES,
  TOAST_POSITIONS,
  ALERT_POSITIONS,
  SESSION_LIFETIME_HOURS,
  PASSWORD_EXPIRY_DAYS,
  PASSWORD_REUSE_COUNT,
  PASSWORD_MAXIMUM_ATTEMPTS,
  TWO_FACTOR_SESSION_TIMEOUT_DAYS,
  RECORDS_PER_PAGE,
  DATE_FORMATS,
  TIME_FORMATS,
} = SettingConstant;
const { LANGUAGE, REGION } = LocalisationConstant.LOCALISATION_CATEGORIES;

/**
 * Retrieves settings for a given category and entity.
 *
 * @async
 * @param {Object} request - The request object containing server and entity information.
 * @param {Object} request.server - The server object used for database operations.
 * @param {Object} request.entity - The entity object containing id and accessLevel.
 * @param {string} category - The category of settings to retrieve.
 * @returns {Promise<Array>} A promise that resolves to an array of found settings.
 * @throws {SettingError} Throws a SettingError if no settings are found for the given category.
 */
export const index = async (request, category) => {
  const {
    server,
    entity: { id: entityId, accessLevel },
    authInfo: { id: authInfoId },
  } = request;

  const queryEntityId = category === PERSONAL ? authInfoId : entityId;

  const query = {
    'filter_customSettings.entityId_eq': queryEntityId,
    filter_category_eq: category,
    filter_accessLevels_overlap: accessLevel,
  };

  const settings = await SettingRepository.findByCategoryAndAccess(server, query);
  if (!settings || settings.length === 0) {
    throw SettingError.notFound(category);
  }

  return settings;
};

/**
 * Updates settings for a given category based on the request body.
 *
 * @async
 * @param {Object} request - The request object containing body, server, entity, and user information.
 * @param {Object} request.body - The request body containing the settings to be updated.
 * @param {Object} request.server - The server object used for database operations.
 * @param {Object} request.entity - The entity object containing the entity ID.
 * @param {Object} request.authInfo - The user object containing the user ID.
 * @param {string} category - The category of settings to be updated.
 * @returns {Promise<void>} A promise that resolves when the settings have been updated.
 */
export const update = async (request, category) => {
  const {
    body,
    server,
    entity: { id: entityId },
    authInfo: { id: authInfoId },
  } = request;
  const { version, ...settingsToUpdate } = body;

  const beforeState = {};
  const afterState = {};
  const fieldsChanged = [];

  const settings = await index(request, category);

  const upsertEntityId = category === PERSONAL ? authInfoId : entityId;

  const bodyMap = new Map(Object.entries(settingsToUpdate));

  const updatedSettings = settings.flatMap((setting) => {
    const value = bodyMap.get(setting.field);

    if (value !== undefined) {
      if (setting.customSettings?.value !== String(value)) {
        beforeState[setting.field] = {
          id: setting.id,
          value: setting.customSettings?.value ?? null,
        };

        afterState[setting.field] = String(value);

        fieldsChanged.push(setting.field);
      }

      return [
        {
          parentId: setting.id,
          entityId: upsertEntityId,
          value: String(value),
          version,
        },
      ];
    }

    return [];
  });

  await withTransaction(server, {}, async (transaction) => {
    await SettingRepository.upsertCustomSettings(server, updatedSettings, {
      transaction,
      authInfoId,
    });
  });

  return {
    result: afterState,
    audit: {
      beforeState,
      afterState,
      fieldsChanged,
    },
  };
};

/**
 * Retrieves a list of personal settings, including language and region options.
 * @param {Object} request - The request object containing params and query information.
 * @returns {Promise<Object>} A promise that resolves to an object containing language and region options.
 */
export const list = async (request, category) => {
  const removeKeys = (obj) => Object.values(obj);

  switch (category) {
    case PERSONAL: {
      const localisations = await getLocalisationOptions(request);
      return {
        appearanceModes: removeKeys(APPEARANCE_MODES),
        preferences: RECORDS_PER_PAGE,
        dateFormat: removeKeys(DATE_FORMATS),
        timeFormat: removeKeys(TIME_FORMATS),
        ...localisations,
      };
    }
    case SAFETY:
      return {
        sessionLifetimeHours: SESSION_LIFETIME_HOURS,
        passwordExpiryDays: PASSWORD_EXPIRY_DAYS,
        passwordReuseCount: PASSWORD_REUSE_COUNT,
        passwordMaximumAttempts: PASSWORD_MAXIMUM_ATTEMPTS,
        twoFactorSessionTimeoutDays: TWO_FACTOR_SESSION_TIMEOUT_DAYS,
      };
    case THEMES:
      return {
        toastPositions: removeKeys(TOAST_POSITIONS),
        alertPositions: removeKeys(ALERT_POSITIONS),
      };
    default:
      throw SettingError.unsupportedInfo(category);
  }
};

/**
 * Retrieves localisation options for language and region.
 *
 * @async
 * @param {Object} request - The request object containing query parameters and server information.
 * @param {Object} request.query - The query parameters from the request.
 * @param {Object} request.server - The server object used for database operations.
 * @returns {Promise<Object>} An object containing localisation options for language and region.
 *                            Each category (LANGUAGE and REGION) is a key in the returned object,
 *                            with its value being an array of options generated by LocalisationService.
 */
const getLocalisationOptions = async (request) => {
  const localisationPromises = [LANGUAGE, REGION].map(async (localisation) => {
    const localisationRequest = {
      ...request,
      query: { ...request.query, 'filter_localisation.category_eq': localisation },
      server: request.server,
    };
    return {
      category: localisation,
      options: await LocalisationService.generateDropdown(localisationRequest),
    };
  });

  const localisationResults = await Promise.all(localisationPromises);
  const localisations = localisationResults.reduce((acc, { category, options }) => {
    acc[category] = options;
    return acc;
  }, {});

  return {
    ...localisations,
  };
};

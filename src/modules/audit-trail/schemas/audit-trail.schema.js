import {
  ACCESS_LEVELS,
  HTTP_METHODS,
  MODULE_NAMES,
} from '#src/modules/core/constants/core.constant.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { createCursorPaginationResponseSchema } from '#src/modules/core/schemas/core.schema.js';

const {
  COMMON_EXPORT_RES_PROPERTIES,
  COMMON_MONGO_PROPERTIES,
  CURSOR_PAGINATION_QUERY_PARAMS,
  ERROR_RESPONSE,
  REQ_PARAM_OBJECT_ID,
  CREATE_RESPONSE,
  VIEW_RESPONSE,
} = CoreSchema;

const { AUDIT_TRAIL } = MODULE_NAMES;

const TAGS = ['BO / Audit Trails'];

/**
 * Response schema properties for audit trail entity.
 */
const AUDIT_TRAIL_RES_PROPERTIES = {
  type: 'object',
  properties: {
    ...COMMON_MON<PERSON><PERSON>_PROPERTIES,
    entityAccessId: { type: 'string' },
    hierarchyLevel: { type: 'string', enum: Object.values(ACCESS_LEVELS) },
    module: { type: 'string' },
    event: { type: 'string' },
    actionKey: { type: 'string' },
    actionParams: { type: 'object', additionalProperties: true },
    actor: {
      type: 'object',
      properties: {
        type: { type: 'string' },
        id: { type: 'string' },
        username: { type: 'string' },
        role: { type: 'string' },
        department: { type: 'string' },
        root: {
          type: ['object', 'null'],
          properties: {
            id: { type: 'string' },
            code: { type: 'string' },
            prefix: { type: 'string' },
            name: { type: 'string' },
          },
        },
        organization: {
          type: ['object', 'null'],
          properties: {
            id: { type: 'string' },
            code: { type: 'string' },
            prefix: { type: 'string' },
            name: { type: 'string' },
          },
        },
        merchant: {
          type: ['object', 'null'],
          properties: {
            id: { type: 'string' },
            code: { type: 'string' },
            prefix: { type: 'string' },
            name: { type: 'string' },
          },
        },
      },
    },
    target: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          referenceId: { type: 'string' },
          referenceDetails: { type: 'object', additionalProperties: true },
          model: { type: 'string' },
          changes: {
            type: 'object',
            properties: {
              beforeState: { type: 'object', additionalProperties: true },
              afterState: { type: 'object', additionalProperties: true },
            },
          },
        },
      },
    },
    details: {
      type: 'object',
      properties: {
        request: {
          type: 'object',
          properties: {
            requestId: { type: 'string' },
            statusCode: { type: 'integer' },
            path: { type: 'string' },
            method: { type: 'string', enum: Object.values(HTTP_METHODS) },
            parameters: { type: 'object', additionalProperties: true },
            payload: { type: 'object', additionalProperties: true },
          },
        },
        metrics: { type: 'object', additionalProperties: true },
        error: { type: 'object', additionalProperties: true },
      },
    },
    context: {
      type: 'object',
      properties: {
        ip: {
          type: 'string',
          oneOf: [{ format: 'ipv4' }, { format: 'ipv6' }, { const: 'UNKNOWN_IP' }],
        },
        location: { type: 'string' },
        userAgent: { type: 'string' },
        device: { type: 'string' },
        fingerprintId: { type: 'string' },
        host: { type: 'string' },
        origin: { type: 'string' },
      },
    },
    status: { type: 'string' },
    descriptionKey: { type: 'string' },
    descriptionParams: { type: 'object', additionalProperties: true },
  },
};
//  # Can be referenced via '#/components/parameters/offsetParam'
const AUDIT_TRAIL_FILTER_QUERYSTRING = {
  type: 'object',
  required: [
    'filter_entityAccessId_eq',
    'filter_hierarchyLevel_eq',
    'filter_timestamp_gte',
    'filter_timestamp_lte',
  ],
  properties: {
    ...CURSOR_PAGINATION_QUERY_PARAMS,
    filter_entityAccessId_eq: { type: 'string', description: 'Filter audit trails by entity' },
    filter_hierarchyLevel_eq: {
      type: 'string',
      enum: Object.values(ACCESS_LEVELS),
      description: 'Filter audit trails by hierarchy level',
    },
    filter_timestamp_gte: {
      type: 'string',
      format: 'date-time',
      description: 'Filter audit trails by timestamp: Start Datetime',
    },
    filter_timestamp_lte: {
      type: 'string',
      format: 'date-time',
      description: 'Filter audit trails by timestamp: End Datetime',
    },
    filter_module_eq: { type: 'string', description: 'Filter audit trails by module requested' },
    filter_event_eq: { type: 'string', description: 'Filter audit trails by event type' },
    filter_action_eq: { type: 'string', description: 'Filter audit trails by action performed' },
    filter_status_eq: {
      type: 'string',
      description: 'Filter audit trails by status of the log entry.',
    },
    'filter_actor.id_eq': {
      type: 'string',
      description: "Filter audit trails by actor's ID",
    },
    'filter_actor.username_eq': {
      type: 'string',
      description: "Filter audit trails by actor's username",
    },
    'filter_actor.type_eq': {
      type: 'string',
      description: "Filter audit trails by actor's type",
    },
    'filter_context.ip_eq': {
      type: 'string',
      oneOf: [{ format: 'ipv4' }, { format: 'ipv6' }],
      description: 'Filter audit trails by IP address',
    },
    'filter_context.host_eq': {
      type: 'string',
      format: 'uri',
      description: 'Filter audit trails by host address',
    },
    'filter_target.referenceId_eq': {
      type: 'string',
      description: 'Filter audit trails by target reference ID',
    },
    'filter_details.request.path_like': {
      type: 'string',
      description: 'Filter audit trails by API endpoint that triggered the action',
    },
  },
};

/**
 * List endpoint schema for audit trails.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${AUDIT_TRAIL}`,
  querystring: AUDIT_TRAIL_FILTER_QUERYSTRING,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: AUDIT_TRAIL_RES_PROPERTIES,
        },
        meta: createCursorPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * View endpoint schema for audit trail.
 */
export const view = {
  tags: TAGS,
  summary: `View a ${AUDIT_TRAIL}`,
  params: REQ_PARAM_OBJECT_ID,
  querystring: {
    type: 'object',
    required: ['filter_timestamp_eq'],
    properties: {
      filter_timestamp_eq: {
        type: 'string',
        format: 'date-time',
        description: 'Filter audit trails by timestamp: Specific Datetime',
      },
    },
  },
  response: VIEW_RESPONSE(AUDIT_TRAIL_RES_PROPERTIES),
};

/**
 * Export audit trail schema.
 */
export const exportAuditTrails = {
  tags: TAGS,
  summary: `Export ${AUDIT_TRAIL}`,
  querystring: AUDIT_TRAIL_FILTER_QUERYSTRING,
  response: CREATE_RESPONSE(COMMON_EXPORT_RES_PROPERTIES),
};
